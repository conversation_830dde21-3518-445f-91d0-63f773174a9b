package services

import (
	"fmt"
	"gofly/app/client/entity"
	"gofly/setting"
	"gofly/utils/gf"
	"gofly/utils/tools/gconv"
	"time"
)

// UserInboxSourceRelatedService 用户收件箱来源关联服务
type UserInboxSourceRelatedService struct {
	keywordService *CrawlerKeywordService
}

// convertStringToSourceType 将字符串类型转换为 SourceType 枚举
// 用于保持向后兼容性
func convertStringToSourceType(typeStr string) entity.SourceType {
	switch typeStr {
	case "video":
		return entity.SourceTypeKeyword
	case "author":
		return entity.SourceTypeAuthor
	case "collect":
		return entity.SourceTypeCollect
	case "KEYWORD":
		return entity.SourceTypeKeyword
	case "AUTHOR":
		return entity.SourceTypeAuthor
	case "COLLECT":
		return entity.SourceTypeCollect
	default:
		return entity.SourceTypeKeyword // 默认值
	}
}

// convertSourceTypeToString 将 SourceType 枚举转换为字符串
// 用于数据库查询等需要字符串的场景
func convertSourceTypeToString(sourceType entity.SourceType) string {
	return string(sourceType)
}

// NewUserInboxSourceRelatedService 创建用户收件箱来源关联服务实例
func NewUserInboxSourceRelatedService() *UserInboxSourceRelatedService {
	return &UserInboxSourceRelatedService{
		keywordService: NewCrawlerKeywordService(),
	}
}

// AddKeywordForUser 为用户添加关键词
func (s *UserInboxSourceRelatedService) AddKeywordForUser(userUUID, keyword string) (*entity.UserInboxSourceRelated, error) {
	return s.AddKeywordForUserWithType(userUUID, keyword, "video")
}

// AddKeywordForUserWithType 为用户添加指定类型的关键词
func (s *UserInboxSourceRelatedService) AddKeywordForUserWithType(userUUID, keyword, crawlerType string) (*entity.UserInboxSourceRelated, error) {
	// 验证参数
	if userUUID == "" {
		return nil, fmt.Errorf("用户UUID不能为空")
	}
	if keyword == "" {
		return nil, fmt.Errorf("关键词不能为空")
	}
	if crawlerType != "video" && crawlerType != "author" && crawlerType != "collect" {
		return nil, fmt.Errorf("不支持的关键词类型: %s", crawlerType)
	}

	// 直接使用 keyword 作为 source_id，不再查询 trendinsight 表
	sourceId := keyword

	// 检查用户是否已经添加了这个关键词和类型的组合
	existing, err := s.GetByUserKeywordAndType(userUUID, sourceId, crawlerType)
	if err == nil && existing != nil {
		// 已存在，直接返回
		return existing, nil
	}

	// 创建用户关键词关联
	userKeyword := &entity.UserInboxSourceRelated{
		UserUUID:   userUUID,
		SourceId:   sourceId,
		SourceType: convertStringToSourceType(crawlerType),
	}

	err = s.Create(userKeyword)
	if err != nil {
		return nil, fmt.Errorf("创建用户关键词关联失败: %w", err)
	}

	return userKeyword, nil
}

// Create 创建用户关键词关联
func (s *UserInboxSourceRelatedService) Create(userKeyword *entity.UserInboxSourceRelated) error {
	// 执行创建前钩子
	if err := userKeyword.BeforeCreate(); err != nil {
		return err
	}

	_, err := gf.Model("user_inbox_source_related").Insert(userKeyword)
	return err
}

// GetByUserAndKeyword 根据用户和关键词获取关联
func (s *UserInboxSourceRelatedService) GetByUserAndKeyword(userUUID string, sourceId string) (*entity.UserInboxSourceRelated, error) {
	// 先查询数量，判断是否存在记录
	count, err := gf.Model("user_inbox_source_related").
		Where("user_uuid", userUUID).
		Where("source_id", sourceId).
		Where("deleted_at", "").
		Count()

	if err != nil {
		return nil, err
	}

	// 如果没有记录，直接返回 nil
	if count == 0 {
		return nil, nil
	}

	// 有记录才执行 Scan 操作
	var userKeyword entity.UserInboxSourceRelated
	err = gf.Model("user_inbox_source_related").
		Where("user_uuid", userUUID).
		Where("source_id", sourceId).
		Where("deleted_at", "").
		Scan(&userKeyword)

	if err != nil {
		return nil, err
	}

	// 如果查询结果为空，返回 nil
	if userKeyword.UUID == "" {
		return nil, nil
	}

	return &userKeyword, nil
}

// GetByUserKeywordAndType 根据用户、关键词和类型获取关联
func (s *UserInboxSourceRelatedService) GetByUserKeywordAndType(userUUID string, sourceId string, crawlerType string) (*entity.UserInboxSourceRelated, error) {
	// 转换字符串类型为枚举类型
	sourceType := convertStringToSourceType(crawlerType)
	// 先查询数量，判断是否存在记录
	count, err := gf.Model("user_inbox_source_related").
		Where("user_uuid", userUUID).
		Where("source_id", sourceId).
		Where("source_type", convertSourceTypeToString(sourceType)).
		Where("deleted_at", "").
		Count()

	if err != nil {
		return nil, err
	}

	// 如果没有记录，直接返回 nil
	if count == 0 {
		return nil, nil
	}

	// 有记录才执行 Scan 操作
	var userKeyword entity.UserInboxSourceRelated
	err = gf.Model("user_inbox_source_related").
		Where("user_uuid", userUUID).
		Where("source_id", sourceId).
		Where("source_type", convertSourceTypeToString(sourceType)).
		Where("deleted_at", "").
		Scan(&userKeyword)

	if err != nil {
		return nil, err
	}

	// 如果查询结果为空，返回 nil
	if userKeyword.UUID == "" {
		return nil, nil
	}

	return &userKeyword, nil
}

// GetByID 根据ID获取用户关键词关联
func (s *UserInboxSourceRelatedService) GetByID(id int) (*entity.UserInboxSourceRelated, error) {
	var userKeyword entity.UserInboxSourceRelated
	err := gf.Model("user_inbox_source_related").
		Where("id", id).
		Where("deleted_at", "").
		Scan(&userKeyword)

	if err != nil {
		return nil, err
	}

	return &userKeyword, nil
}

// GetByUUID 根据UUID获取用户关键词关联
func (s *UserInboxSourceRelatedService) GetByUUID(uuid string) (*entity.UserInboxSourceRelated, error) {
	var userKeyword entity.UserInboxSourceRelated
	err := gf.Model("user_inbox_source_related").
		Where("uuid", uuid).
		Where("deleted_at", "").
		Scan(&userKeyword)

	if err != nil {
		return nil, err
	}

	return &userKeyword, nil
}

// GetByUserAndKeywordText 根据用户和关键词文本获取关联
func (s *UserInboxSourceRelatedService) GetByUserAndKeywordText(userUUID, keyword string) (*entity.UserInboxSourceRelated, error) {
	// 直接使用关键词文本作为 source_id 查询
	var userKeyword entity.UserInboxSourceRelated
	err := gf.Model("user_inbox_source_related").
		Where("user_uuid", userUUID).
		Where("source_id", keyword).
		Where("deleted_at", "").
		Scan(&userKeyword)

	if err != nil {
		return nil, fmt.Errorf("查询用户关键词关联失败: %v", err)
	}

	// 如果查询结果为空，返回 nil
	if userKeyword.UUID == "" {
		return nil, nil
	}

	return &userKeyword, nil
}

// Update 更新用户关键词关联
func (s *UserInboxSourceRelatedService) Update(userKeyword *entity.UserInboxSourceRelated) error {
	_, err := gf.Model("user_inbox_source_related").
		Where("uuid", userKeyword.UUID).
		Update(userKeyword)
	return err
}

// Delete 软删除用户关键词关联
func (s *UserInboxSourceRelatedService) Delete(id int) error {
	now := time.Now()
	_, err := gf.Model("user_inbox_source_related").
		Where("id", id).
		Update(gf.Map{
			"deleted_at":  now.Format("2006-01-02 15:04:05.000000"),
			"update_time": now,
		})
	return err
}

// DeleteByUUID 通过UUID软删除用户关键词关联
func (s *UserInboxSourceRelatedService) DeleteByUUID(uuid string) error {
	now := time.Now()
	_, err := gf.Model("user_inbox_source_related").
		Where("uuid", uuid).
		Update(gf.Map{
			"deleted_at":  now.Format("2006-01-02 15:04:05.000000"),
			"update_time": now,
		})
	return err
}

// GetUserKeywords 获取用户的关键词列表
// 拆分为两次查询避免跨数据库联表问题
func (s *UserInboxSourceRelatedService) GetUserKeywords(userUUID string, page, pageSize int) ([]*entity.UserInboxSourceRelated, int64, error) {
	query := gf.Model("user_inbox_source_related").
		Where("user_uuid", userUUID).
		Where("deleted_at", "")

	// 获取总数
	total, err := query.Count()
	if err != nil {
		return nil, 0, err
	}

	// 分页查询用户关键词关联
	var userKeywords []*entity.UserInboxSourceRelated
	err = query.
		Order("create_time DESC").
		Limit((page-1)*pageSize, pageSize).
		Scan(&userKeywords)

	if err != nil {
		return nil, 0, err
	}

	// 填充关键词详情
	err = s.FillKeywordDetails(userKeywords)
	if err != nil {
		return nil, 0, fmt.Errorf("填充关键词详情失败: %v", err)
	}

	return userKeywords, int64(total), nil
}

// GetUserKeywordsByType 根据类型获取用户的关键词列表
func (s *UserInboxSourceRelatedService) GetUserKeywordsByType(userUUID, crawlerType string, page, pageSize int) ([]*entity.UserInboxSourceRelated, int64, error) {
	// 转换字符串类型为枚举类型
	sourceType := convertStringToSourceType(crawlerType)
	query := gf.Model("user_inbox_source_related").
		Where("user_uuid", userUUID).
		Where("source_type", convertSourceTypeToString(sourceType)).
		Where("deleted_at", "")

	// 获取总数
	total, err := query.Count()
	if err != nil {
		return nil, 0, err
	}

	// 分页查询用户关键词关联
	var userKeywords []*entity.UserInboxSourceRelated
	err = query.
		Order("create_time DESC").
		Limit((page-1)*pageSize, pageSize).
		Scan(&userKeywords)

	if err != nil {
		return nil, 0, err
	}

	// 填充关键词详情
	err = s.FillKeywordDetails(userKeywords)
	if err != nil {
		return nil, 0, fmt.Errorf("填充关键词详情失败: %v", err)
	}

	return userKeywords, int64(total), nil
}

// FillKeywordDetails 为用户关键词列表填充详细信息（支持视频和作者类型）
func (s *UserInboxSourceRelatedService) FillKeywordDetails(userKeywords []*entity.UserInboxSourceRelated) error {
	if len(userKeywords) == 0 {
		return nil
	}

	// 收集视频类型的关键词ID，用于批量查询关键词详情
	var videoKeywordIDs []string
	videoIndexMap := make(map[string][]*entity.UserInboxSourceRelated) // 关键词ID -> 关键词列表的映射

	// 收集作者类型的用户ID，用于批量查询作者详情
	var authorUserIDs []string
	authorIndexMap := make(map[string][]*entity.UserInboxSourceRelated) // 用户ID -> 关键词列表的映射

	for _, uk := range userKeywords {
		if uk.SourceType == entity.SourceTypeKeyword {
			// 收集关键词ID，准备批量查询
			keywordId := uk.SourceId
			if _, exists := videoIndexMap[keywordId]; !exists {
				videoKeywordIDs = append(videoKeywordIDs, keywordId)
				videoIndexMap[keywordId] = make([]*entity.UserInboxSourceRelated, 0)
			}
			videoIndexMap[keywordId] = append(videoIndexMap[keywordId], uk)
		} else if uk.SourceType == entity.SourceTypeAuthor {
			// 收集作者用户ID，准备批量查询
			userID := uk.SourceId
			if _, exists := authorIndexMap[userID]; !exists {
				authorUserIDs = append(authorUserIDs, userID)
				authorIndexMap[userID] = make([]*entity.UserInboxSourceRelated, 0)
			}
			authorIndexMap[userID] = append(authorIndexMap[userID], uk)
		}
	}

	// 如果有视频类型的关键词，从 trendinsight_keyword 表查询详细信息
	if len(videoKeywordIDs) > 0 {
		var trendKeywords []*entity.TrendInsightKeyword
		err := setting.CrawlerModel("trendinsight_keyword").
			Where("id IN (?)", videoKeywordIDs).
			Scan(&trendKeywords)
		if err != nil {
			return fmt.Errorf("查询关键词详情失败: %v", err)
		}

		// 创建关键词信息映射
		keywordMap := make(map[string]*entity.TrendInsightKeyword)
		for _, tk := range trendKeywords {
			keywordMap[gconv.String(tk.ID)] = tk
		}

		// 填充关键词详情
		for keywordId, userKeywords := range videoIndexMap {
			if keyword, exists := keywordMap[keywordId]; exists {
				// 找到了关键词详情，填充到所有相关的用户关键词中
				for _, uk := range userKeywords {
					uk.TrendKeyword = keyword
				}
			} else {
				// 没有找到关键词详情，创建一个简单的关键词对象
				for _, uk := range userKeywords {
					uk.TrendKeyword = &entity.TrendInsightKeyword{
						ID:      gconv.Int(keywordId),
						Keyword: keywordId, // 使用 keyword_id 作为显示名称
					}
				}
			}
		}
	}

	// 如果有作者类型的关键词，从 trendinsight_author 表查询详细信息
	if len(authorUserIDs) > 0 {
		var trendAuthors []*entity.TrendInsightAuthor
		err := setting.CrawlerModel("trendinsight_author").
			Where("user_id IN (?)", authorUserIDs).
			Scan(&trendAuthors)
		if err != nil {
			return fmt.Errorf("查询作者详情失败: %v", err)
		}

		// 创建作者信息映射
		authorMap := make(map[string]*entity.TrendInsightAuthor)
		for _, ta := range trendAuthors {
			authorMap[ta.UserID] = ta
		}

		// 填充作者详情
		for userID, userKeywords := range authorIndexMap {
			if author, exists := authorMap[userID]; exists {
				// 找到了作者详情，填充到所有相关的用户关键词中
				for _, uk := range userKeywords {
					uk.TrendAuthor = author
				}
			} else {
				// 没有找到作者详情，创建一个简单的作者对象
				for _, uk := range userKeywords {
					uk.TrendAuthor = &entity.TrendInsightAuthor{
						UserID:   userID,
						UserName: userID, // 使用 user_id 作为显示名称
					}
				}
			}
		}
	}

	return nil
}

// RestoreByUUID 通过UUID恢复已删除的用户关键词关联
func (s *UserInboxSourceRelatedService) RestoreByUUID(uuid string) error {
	_, err := gf.Model("user_inbox_source_related").
		Where("uuid", uuid).
		Update(gf.Map{
			"deleted_at":  "",
			"update_time": time.Now(),
		})
	return err
}

// RestoreByID 通过ID恢复已删除的用户关键词关联
func (s *UserInboxSourceRelatedService) RestoreByID(id int) error {
	_, err := gf.Model("user_inbox_source_related").
		Where("id", id).
		Update(gf.Map{
			"deleted_at":  "",
			"update_time": time.Now(),
		})
	return err
}

// GetDeletedUserKeywords 获取用户已删除的关键词列表
func (s *UserInboxSourceRelatedService) GetDeletedUserKeywords(userUUID string, page, pageSize int) ([]*entity.UserInboxSourceRelated, int64, error) {
	query := gf.Model("user_inbox_source_related").
		Where("user_uuid", userUUID).
		Where("deleted_at != ?", "")

	// 获取总数
	total, err := query.Count()
	if err != nil {
		return nil, 0, err
	}

	// 分页查询已删除的用户关键词关联
	var userKeywords []*entity.UserInboxSourceRelated
	err = query.
		Order("deleted_at DESC").
		Limit((page-1)*pageSize, pageSize).
		Scan(&userKeywords)

	if err != nil {
		return nil, 0, err
	}

	// 填充关键词详情
	err = s.FillKeywordDetails(userKeywords)
	if err != nil {
		return nil, 0, fmt.Errorf("填充关键词详情失败: %v", err)
	}

	return userKeywords, int64(total), nil
}
