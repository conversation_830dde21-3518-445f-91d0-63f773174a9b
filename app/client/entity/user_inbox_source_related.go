package entity

import (
	"fmt"
	"gofly/utils/gf"
	"gofly/utils/tools/gmeta"
	"time"
)

// SourceType 来源类型枚举
type SourceType string

const (
	// SourceTypeKeyword 关键词类型
	SourceTypeKeyword SourceType = "KEYWORD"
	// SourceTypeAuthor 作者类型
	SourceTypeAuthor SourceType = "AUTHOR"
	// SourceTypeCollect 收藏夹类型
	SourceTypeCollect SourceType = "COLLECT"
)

// String 返回字符串表示
func (st SourceType) String() string {
	return string(st)
}

// IsValid 验证来源类型是否有效
func (st SourceType) IsValid() bool {
	switch st {
	case SourceTypeKeyword, SourceTypeAuthor, SourceTypeCollect:
		return true
	default:
		return false
	}
}

// GetAllSourceTypes 获取所有有效的来源类型
func GetAllSourceTypes() []SourceType {
	return []SourceType{SourceTypeKeyword, SourceTypeAuthor, SourceTypeCollect}
}

// UserInboxSourceRelated 用户收件箱来源关联实体类
// 管理用户与关键词的关联关系，支持多用户使用相同关键词
type UserInboxSourceRelated struct {
	gmeta.Meta `orm:"table:user_inbox_source_related"`
	BaseEntity

	// 关联字段
	UserUUID string `gorm:"column:user_uuid;type:varchar(32);not null;index:idx_user_keyword_user;comment:用户UUID" json:"user_uuid"`
	SourceId string `gorm:"column:source_id;type:varchar(32);not null;index:idx_user_source_id;comment:来源ID" json:"source_id"`

	// 来源类型字段
	SourceType SourceType `gorm:"column:source_type;type:varchar(10);not null;default:'KEYWORD';index:idx_user_source_type;comment:来源类型(KEYWORD/AUTHOR/COLLECT)" json:"source_type"`

	// 软删除字段
	DeletedAt string `gorm:"column:deleted_at;type:varchar(26);default:'';index:idx_user_source_deleted;comment:删除时间" json:"deleted_at,omitempty"`

	// 关联关系（用于联表查询）
	User         *User                `json:"user,omitempty" gorm:"foreignKey:UserUUID;references:UUID"`
	TrendKeyword *TrendInsightKeyword `json:"trend_keyword,omitempty" gorm:"foreignKey:SourceId;references:ID"`
	TrendAuthor  *TrendInsightAuthor  `json:"trend_author,omitempty" gorm:"foreignKey:SourceId;references:ID"`
}

// TableName 返回表名
func (UserInboxSourceRelated) TableName() string {
	return "user_inbox_source_related"
}

// BeforeCreate 创建前的钩子函数
func (uck *UserInboxSourceRelated) BeforeCreate() error {
	// 设置 UUID（如果为空）
	if uck.UUID == "" {
		uck.UUID = gf.GenerateUUID()
	}

	// 设置 SourceType 默认值（如果为空）
	if uck.SourceType == "" {
		uck.SourceType = SourceTypeKeyword
	}

	// 验证 SourceType 字段
	if err := uck.ValidateType(); err != nil {
		return err
	}

	// 设置创建时间和更新时间
	now := time.Now()
	if uck.CreateTime.IsZero() {
		uck.CreateTime = now
	}
	if uck.UpdateTime.IsZero() {
		uck.UpdateTime = now
	}

	return nil
}

// generateUUID 生成UUID的辅助函数
func generateUserInboxSourceUUID() string {
	// 使用时间戳生成32位UUID
	now := time.Now()
	timestamp := now.UnixNano()
	// 生成32位的十六进制字符串
	return fmt.Sprintf("%016x%016x", timestamp, ^timestamp)
}

// GetDisplayName 获取显示名称
func (uck *UserInboxSourceRelated) GetDisplayName() string {
	if uck.IsKeywordType() && uck.TrendKeyword != nil {
		return uck.TrendKeyword.Keyword
	}
	if uck.IsAuthorType() && uck.TrendAuthor != nil {
		return uck.TrendAuthor.UserName
	}
	return ""
}

// GetKeywordName 获取关键词名称（用于视频类型）
func (uck *UserInboxSourceRelated) GetKeywordName() string {
	if uck.TrendKeyword != nil {
		return uck.TrendKeyword.Keyword
	}
	return ""
}

// GetAuthorName 获取作者名称（用于作者类型）
func (uck *UserInboxSourceRelated) GetAuthorName() string {
	if uck.TrendAuthor != nil {
		return uck.TrendAuthor.UserName
	}
	return ""
}

// ValidateType 验证 SourceType 字段是否为有效值
func (uck *UserInboxSourceRelated) ValidateType() error {
	if uck.SourceType.IsValid() {
		return nil
	}
	return fmt.Errorf("invalid type '%s', must be one of: [KEYWORD, AUTHOR, COLLECT]", uck.SourceType)
}

// IsKeywordType 判断是否为关键词类型
func (uck *UserInboxSourceRelated) IsKeywordType() bool {
	return uck.SourceType == SourceTypeKeyword
}

// IsVideoType 判断是否为关键词类型（保持向后兼容）
// Deprecated: 使用 IsKeywordType 替代
func (uck *UserInboxSourceRelated) IsVideoType() bool {
	return uck.IsKeywordType()
}

// IsAuthorType 判断是否为作者类型
func (uck *UserInboxSourceRelated) IsAuthorType() bool {
	return uck.SourceType == SourceTypeAuthor
}

// IsCollectType 判断是否为收藏夹类型
func (uck *UserInboxSourceRelated) IsCollectType() bool {
	return uck.SourceType == SourceTypeCollect
}

// SoftDelete 软删除记录
func (uck *UserInboxSourceRelated) SoftDelete() {
	now := time.Now()
	uck.DeletedAt = now.Format("2006-01-02 15:04:05.000000")
}

// IsDeleted 判断记录是否已被删除
func (uck *UserInboxSourceRelated) IsDeleted() bool {
	return uck.DeletedAt != ""
}

// Restore 恢复已删除的记录
func (uck *UserInboxSourceRelated) Restore() {
	uck.DeletedAt = ""
}

// GetDeletedAt 获取删除时间
func (uck *UserInboxSourceRelated) GetDeletedAt() *time.Time {
	if uck.DeletedAt == "" {
		return nil
	}

	// 解析时间字符串
	t, err := time.Parse("2006-01-02 15:04:05.000000", uck.DeletedAt)
	if err != nil {
		return nil
	}

	return &t
}

// GetDeletedAtTimestamp 获取删除时间的时间戳（秒）
func (uck *UserInboxSourceRelated) GetDeletedAtTimestamp() int64 {
	if uck.DeletedAt == "" {
		return 0
	}

	// 解析时间字符串
	t, err := time.Parse("2006-01-02 15:04:05.000000", uck.DeletedAt)
	if err != nil {
		return 0
	}

	return t.Unix()
}
